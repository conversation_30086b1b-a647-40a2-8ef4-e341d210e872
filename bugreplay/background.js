
/* global chrome */

// background.js

// Import RecordingManager - Note: Chrome extensions don't support ES6 imports in service workers
// We'll need to implement the RecordingManager inline or use a different approach

/**
 * RecordingManager - Centralized recording management
 * Handles coordination between different recording components
 */
class RecordingManager {
  constructor() {
    this.currentSession = null;
    this.state = 'IDLE'; // RecordingState.IDLE
    this.mediaRecorder = null;
    this.recordedChunks = [];
    this.screenshotInterval = null;
    this.networkEntries = [];
    this.harData = null;

    // Default settings
    this.settings = {
      automaticScreenshots: true,
      videoRecording: true,
      networkMonitoring: true,
      videoSaveLocation: ''
    };

    // Bind methods
    this.handleNetworkRequest = this.handleNetworkRequest.bind(this);
    this.handleRequestHeaders = this.handleRequestHeaders.bind(this);
    this.handleResponseHeaders = this.handleResponseHeaders.bind(this);
    this.handleNetworkResponse = this.handleNetworkResponse.bind(this);
    this.handleNetworkError = this.handleNetworkError.bind(this);
    this.captureScreenshot = this.captureScreenshot.bind(this);

    // Load settings on initialization
    this.loadSettings();
  }

  /**
   * Start a new recording session with improved state management
   * @param {number} tabId - ID of the tab to record
   * @returns {Promise<string>} Session ID
   */
  async startRecording(tabId) {
    console.log(`BugReplay: Attempting to start recording. Current state: ${this.state}`);

    // Check if recording is already in progress
    if (this.state === 'RECORDING') {
      console.warn('BugReplay: Recording already in progress, returning existing session');
      if (this.currentSession) {
        return this.currentSession.id;
      }
    }

    // If in any other non-idle state, force reset to idle
    if (this.state !== 'IDLE') {
      console.warn(`BugReplay: Force resetting state from ${this.state} to IDLE`);
      await this.forceResetState();
    }

    this.state = 'STARTING';

    try {
      // Get tab information
      const tab = await this.getTabInfo(tabId);

      // Create new session
      this.currentSession = {
        id: this.generateSessionId(),
        startTime: new Date(),
        endTime: null,
        tabId: tabId,
        url: tab.url,
        title: tab.title,
        logs: [],
        screenshots: [],
        videoUrl: null,
        harData: null,
        state: 'STARTING'
      };

      // Initialize recording components based on settings
      if (this.settings.networkMonitoring) {
        await this.initializeNetworkMonitoring();
      }

      if (this.settings.automaticScreenshots) {
        await this.startScreenshotCapture();
      }

      // Start video recording automatically if enabled
      if (this.settings.videoRecording) {
        await this.startVideoRecording();
      }

      this.state = 'RECORDING';
      this.currentSession.state = 'RECORDING';

      // Store session in chrome storage
      await this.saveSession();

      console.log('Recording started for session:', this.currentSession.id);
      return this.currentSession.id;

    } catch (error) {
      this.state = 'ERROR';
      console.error('Failed to start recording:', error);

      // Reset state after error
      setTimeout(() => {
        if (this.state === 'ERROR') {
          this.forceResetState();
        }
      }, 5000);

      throw error;
    }
  }

  /**
   * Force reset the recording state to IDLE
   */
  async forceResetState() {
    console.log(`BugReplay: Force resetting state from ${this.state} to IDLE`);

    try {
      // Stop any ongoing recording components
      if (this.state === 'RECORDING' || this.state === 'STOPPING') {
        await this.stopNetworkMonitoring().catch(() => {});
        this.stopScreenshotCapture();
        await this.stopVideoRecording().catch(() => {});
      }

      // Clear current session
      this.currentSession = null;
      this.state = 'IDLE';
      this.networkEntries = [];
      this.harData = null;

      // Clear storage state
      await chrome.storage.local.remove(['currentSessionId', 'recordingState']);

      console.log('BugReplay: State reset to IDLE successfully');
    } catch (error) {
      console.error('BugReplay: Error during force reset:', error);
      // Force state to IDLE even if cleanup fails
      this.currentSession = null;
      this.state = 'IDLE';
    }
  }

  /**
   * Stop the current recording session with improved error handling
   * @returns {Promise<RecordingSession>} Completed session data
   */
  async stopRecording() {
    console.log(`BugReplay: Attempting to stop recording. Current state: ${this.state}`);

    if (this.state === 'IDLE') {
      throw new Error('No recording in progress');
    }

    if (this.state === 'STOPPING') {
      console.warn('BugReplay: Recording already stopping, waiting for completion...');
      // Wait for current stop operation to complete
      return new Promise((resolve, reject) => {
        const checkInterval = setInterval(() => {
          if (this.state === 'IDLE') {
            clearInterval(checkInterval);
            resolve(null);
          } else if (this.state === 'ERROR') {
            clearInterval(checkInterval);
            reject(new Error('Recording stop failed'));
          }
        }, 100);

        // Timeout after 10 seconds
        setTimeout(() => {
          clearInterval(checkInterval);
          reject(new Error('Recording stop timeout'));
        }, 10000);
      });
    }

    if (this.state !== 'RECORDING') {
      console.warn(`BugReplay: Unexpected state during stop: ${this.state}`);
      await this.forceResetState();
      throw new Error('Invalid recording state');
    }

    this.state = 'STOPPING';

    try {
      // Stop recording components based on what was started
      if (this.settings.networkMonitoring) {
        await this.stopNetworkMonitoring();
      }

      if (this.settings.automaticScreenshots) {
        this.stopScreenshotCapture();
      }

      if (this.settings.videoRecording) {
        await this.stopVideoRecording();
      }

      // Finalize session
      this.currentSession.endTime = new Date();
      this.currentSession.state = 'PROCESSING';

      // Process and compile data
      await this.processRecordingData();

      this.currentSession.state = 'COMPLETED';

      // Save completed session permanently
      await this.saveCompletedSession();

      const completedSession = { ...this.currentSession };
      const sessionId = completedSession.id;

      // Clear current session from active recording
      this.currentSession = null;
      this.state = 'IDLE';

      // Clear only the active recording state, keep the completed session
      await this.clearActiveSession();

      console.log('Recording stopped and saved for session:', completedSession.id);
      return completedSession;

    } catch (error) {
      console.error('Failed to stop recording:', error);

      // Force reset state on error
      await this.forceResetState();
      throw error;
    }
  }

  /**
   * Initialize comprehensive network monitoring using webRequest API
   */
  async initializeNetworkMonitoring() {
    this.networkEntries = [];
    this.requestTimings = new Map();
    this.requestBodies = new Map();
    this.responseBodies = new Map();

    // Monitor request start (onBeforeRequest) - capture request body
    chrome.webRequest.onBeforeRequest.addListener(
      this.handleNetworkRequest.bind(this),
      { urls: ['<all_urls>'] },
      ['requestBody']
    );

    // Monitor request headers sent (onBeforeSendHeaders) - capture request headers
    chrome.webRequest.onBeforeSendHeaders.addListener(
      this.handleRequestHeaders.bind(this),
      { urls: ['<all_urls>'] },
      ['requestHeaders', 'extraHeaders']
    );

    // Monitor response headers received (onHeadersReceived) - capture response headers
    chrome.webRequest.onHeadersReceived.addListener(
      this.handleResponseHeaders.bind(this),
      { urls: ['<all_urls>'] },
      ['responseHeaders', 'extraHeaders']
    );

    // Monitor response completion (onCompleted) - capture final timing
    chrome.webRequest.onCompleted.addListener(
      this.handleNetworkResponse.bind(this),
      { urls: ['<all_urls>'] },
      ['responseHeaders']
    );

    // Monitor request errors (onErrorOccurred) - capture failed requests
    chrome.webRequest.onErrorOccurred.addListener(
      this.handleNetworkError.bind(this),
      { urls: ['<all_urls>'] }
    );

    // Monitor redirects (onBeforeRedirect) - capture redirect information
    chrome.webRequest.onBeforeRedirect.addListener(
      this.handleNetworkRedirect.bind(this),
      { urls: ['<all_urls>'] },
      ['responseHeaders']
    );

    console.log('BugReplay: Enhanced network monitoring initialized with comprehensive data capture');
  }

  /**
   * Stop network monitoring
   */
  async stopNetworkMonitoring() {
    try {
      chrome.webRequest.onBeforeRequest.removeListener(this.handleNetworkRequest.bind(this));
      chrome.webRequest.onBeforeSendHeaders.removeListener(this.handleRequestHeaders.bind(this));
      chrome.webRequest.onHeadersReceived.removeListener(this.handleResponseHeaders.bind(this));
      chrome.webRequest.onCompleted.removeListener(this.handleNetworkResponse.bind(this));
      chrome.webRequest.onErrorOccurred.removeListener(this.handleNetworkError.bind(this));
      chrome.webRequest.onBeforeRedirect.removeListener(this.handleNetworkRedirect.bind(this));
    } catch (error) {
      console.warn('BugReplay: Error removing network listeners:', error);
    }

    // Generate comprehensive HAR data
    this.generateHarData();

    console.log(`BugReplay: Network monitoring stopped, HAR data generated with ${this.networkEntries.length} entries`);
  }

  /**
   * Handle network request start
   * @param {Object} details - Request details
   */
  handleNetworkRequest(details) {
    if (!this.currentSession || this.state !== 'RECORDING') return;

    // Filter out extension requests and chrome:// URLs
    if (details.url.startsWith('chrome://') ||
        details.url.startsWith('chrome-extension://') ||
        details.url.startsWith('moz-extension://')) {
      return;
    }

    const entry = {
      requestId: details.requestId,
      url: details.url,
      method: details.method,
      startTime: details.timeStamp,
      requestBody: details.requestBody,
      tabId: details.tabId,
      type: details.type,
      frameId: details.frameId,
      parentFrameId: details.parentFrameId,
      initiator: details.initiator,
      // Enhanced timing data
      timings: {
        blocked: -1,
        dns: -1,
        connect: -1,
        send: -1,
        wait: -1,
        receive: -1,
        ssl: -1
      },
      // Initialize headers with better structure
      requestHeaders: [],
      responseHeaders: [],
      // Enhanced response data
      status: 0,
      statusText: '',
      mimeType: '',
      size: 0,
      transferSize: 0,
      error: null,
      redirects: [],
      // Additional metadata for better HAR compliance
      httpVersion: 'HTTP/1.1',
      cookies: [],
      queryString: [],
      postData: null
    };

    this.networkEntries.push(entry);

    // Store timing reference
    this.requestTimings.set(details.requestId, {
      startTime: details.timeStamp,
      blocked: details.timeStamp
    });

    // Add to session logs
    this.addLogEntry({
      type: 'NETWORK_REQUEST',
      message: `${details.method} ${details.url}`,
      timestamp: new Date(details.timeStamp)
    });
  }

  /**
   * Handle request headers being sent
   * @param {Object} details - Request header details
   */
  handleRequestHeaders(details) {
    if (!this.currentSession || this.state !== 'RECORDING') return;

    const entry = this.networkEntries.find(e => e.requestId === details.requestId);
    if (entry) {
      entry.requestHeaders = details.requestHeaders || [];

      // Update timing
      const timing = this.requestTimings.get(details.requestId);
      if (timing) {
        timing.send = details.timeStamp;
        entry.timings.send = details.timeStamp - timing.startTime;
      }
    }
  }

  /**
   * Handle response headers received
   * @param {Object} details - Response header details
   */
  handleResponseHeaders(details) {
    if (!this.currentSession || this.state !== 'RECORDING') return;

    const entry = this.networkEntries.find(e => e.requestId === details.requestId);
    if (entry) {
      entry.responseHeaders = details.responseHeaders || [];
      entry.status = details.statusCode;
      entry.statusText = details.statusLine || '';

      // Extract content type
      const contentTypeHeader = details.responseHeaders?.find(h =>
        h.name.toLowerCase() === 'content-type'
      );
      if (contentTypeHeader) {
        entry.mimeType = contentTypeHeader.value.split(';')[0].trim();
      }

      // Update timing
      const timing = this.requestTimings.get(details.requestId);
      if (timing) {
        timing.wait = details.timeStamp;
        entry.timings.wait = details.timeStamp - (timing.send || timing.startTime);
      }
    }
  }

  /**
   * Handle network response completion
   * @param {Object} details - Response completion details
   */
  handleNetworkResponse(details) {
    if (!this.currentSession || this.state !== 'RECORDING') return;

    const entry = this.networkEntries.find(e => e.requestId === details.requestId);
    if (entry) {
      entry.status = details.statusCode;
      entry.statusText = details.statusLine || '';
      entry.endTime = details.timeStamp;
      entry.size = details.responseSize || 0;
      entry.transferSize = details.responseSize || 0;

      // Update final timing
      const timing = this.requestTimings.get(details.requestId);
      if (timing) {
        entry.timings.receive = details.timeStamp - (timing.wait || timing.send || timing.startTime);
        entry.timings.total = details.timeStamp - timing.startTime;
      }

      // Clean up timing reference
      this.requestTimings.delete(details.requestId);
    }

    // Add to session logs
    this.addLogEntry({
      type: 'NETWORK_RESPONSE',
      message: `${details.statusCode} ${details.url}`,
      status: details.statusCode >= 400 ? 'error' : 'success',
      timestamp: new Date(details.timeStamp)
    });
  }

  /**
   * Handle network errors
   * @param {Object} details - Error details
   */
  handleNetworkError(details) {
    if (!this.currentSession || this.state !== 'RECORDING') return;

    const entry = this.networkEntries.find(e => e.requestId === details.requestId);
    if (entry) {
      entry.error = details.error;
      entry.endTime = details.timeStamp;
      entry.status = 0;
      entry.statusText = details.error;

      // Clean up timing reference
      this.requestTimings.delete(details.requestId);
    }

    // Add to session logs
    this.addLogEntry({
      type: 'NETWORK_ERROR',
      message: `Network error: ${details.error} for ${details.url}`,
      status: 'error',
      timestamp: new Date(details.timeStamp)
    });
  }

  /**
   * Handle network request redirect
   * @param {Object} details - Redirect details
   */
  handleNetworkRedirect(details) {
    if (!this.currentSession || this.state !== 'RECORDING') return;

    const entry = this.networkEntries.find(e => e.requestId === details.requestId);
    if (entry) {
      // Record redirect information
      entry.redirects.push({
        url: details.redirectUrl,
        status: details.statusCode,
        statusText: details.statusLine,
        headers: details.responseHeaders || [],
        timestamp: details.timeStamp
      });

      // Update response headers for the redirect
      if (details.responseHeaders) {
        entry.responseHeaders = details.responseHeaders;
      }

      this.addLogEntry({
        type: 'NETWORK_REDIRECT',
        message: `Redirect: ${details.url} → ${details.redirectUrl} (${details.statusCode})`,
        timestamp: new Date(details.timeStamp)
      });
    }
  }

  /**
   * Start periodic screenshot capture
   */
  async startScreenshotCapture() {
    this.screenshotInterval = setInterval(async () => {
      try {
        await this.captureScreenshot();
      } catch (error) {
        console.warn('Failed to capture screenshot:', error);
      }
    }, 5000); // Capture every 5 seconds
  }

  /**
   * Stop screenshot capture
   */
  stopScreenshotCapture() {
    if (this.screenshotInterval) {
      clearInterval(this.screenshotInterval);
      this.screenshotInterval = null;
    }
  }

  /**
   * Capture a screenshot of the active tab
   */
  async captureScreenshot(isManual = false) {
    return new Promise((resolve, reject) => {
      chrome.tabs.captureVisibleTab(null, { format: 'png' }, (dataUrl) => {
        if (chrome.runtime.lastError || !dataUrl) {
          reject(new Error(chrome.runtime.lastError?.message || 'Screenshot failed'));
          return;
        }

        let screenshot;
        if (isManual) {
          // Manual screenshots use object format with metadata
          screenshot = {
            dataUrl: dataUrl,
            timestamp: new Date(),
            isManual: true,
            id: Date.now() + Math.random()
          };
        } else {
          // Automatic screenshots use string format for backward compatibility
          screenshot = dataUrl;
        }

        this.currentSession.screenshots.push(screenshot);
        this.addLogEntry({
          type: 'SCREENSHOT',
          message: isManual ? 'Manual screenshot captured' : 'Automatic screenshot captured',
          timestamp: new Date(),
          isManual: isManual
        });

        resolve(isManual ? screenshot : { dataUrl: screenshot, isManual: false });
      });
    });
  }

  /**
   * Capture a manual screenshot triggered by user
   */
  async captureManualScreenshot() {
    try {
      const screenshot = await this.captureScreenshot(true);

      // Save manual screenshot to downloads if configured
      await this.saveManualScreenshot(screenshot);

      return screenshot;
    } catch (error) {
      console.error('BugReplay: Failed to capture manual screenshot:', error);
      this.addLogEntry({
        type: 'ERROR',
        message: `Failed to capture manual screenshot: ${error.message}`,
        timestamp: new Date()
      });
      throw error;
    }
  }

  /**
   * Save manual screenshot to local storage (downloads)
   */
  async saveManualScreenshot(screenshot) {
    try {
      if (!this.currentSession || !screenshot) return;

      const fileName = `bugreplay-screenshot-${this.currentSession.id}-${Date.now()}.png`;

      // Convert data URL to blob
      const response = await fetch(screenshot.dataUrl);
      const blob = await response.blob();

      // Create download link
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;

      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up
      URL.revokeObjectURL(url);

      this.addLogEntry({
        type: 'SYSTEM',
        message: `Manual screenshot saved as ${fileName}`,
        timestamp: new Date()
      });

    } catch (error) {
      console.error('BugReplay: Error saving manual screenshot:', error);
      this.addLogEntry({
        type: 'ERROR',
        message: `Error saving manual screenshot: ${error.message}`,
        timestamp: new Date()
      });
    }
  }

  /**
   * Start video recording using getDisplayMedia API
   */
  async startVideoRecording() {
    try {
      // Send message to content script to start video recording
      if (this.currentSession && this.currentSession.tabId) {
        chrome.tabs.sendMessage(this.currentSession.tabId, {
          type: 'START_VIDEO_RECORDING',
          sessionId: this.currentSession.id
        }, (response) => {
          if (chrome.runtime.lastError) {
            console.warn('BugReplay: Could not start video recording from content script:', chrome.runtime.lastError.message);
            this.addLogEntry({
              type: 'SYSTEM',
              message: 'Video recording requires user interaction - click "Start Video" in popup',
              timestamp: new Date()
            });
          } else if (response && response.success) {
            this.addLogEntry({
              type: 'SYSTEM',
              message: 'Video recording started automatically',
              timestamp: new Date()
            });
            this.currentSession.videoRecording = true;

            // Notify popup about video recording status
            this.notifyVideoRecordingStatus(true);
          } else {
            this.addLogEntry({
              type: 'SYSTEM',
              message: response?.error || 'Video recording failed to start',
              timestamp: new Date()
            });
          }
        });
      }
    } catch (error) {
      console.error('BugReplay: Failed to start video recording:', error);
      this.addLogEntry({
        type: 'ERROR',
        message: `Failed to start video recording: ${error.message}`,
        timestamp: new Date()
      });
    }
  }

  /**
   * Stop video recording
   */
  async stopVideoRecording() {
    try {
      // Send message to content script to stop video recording
      if (this.currentSession && this.currentSession.tabId) {
        chrome.tabs.sendMessage(this.currentSession.tabId, {
          type: 'STOP_VIDEO_RECORDING',
          sessionId: this.currentSession.id
        }, (response) => {
          if (chrome.runtime.lastError) {
            console.warn('BugReplay: Could not stop video recording from content script:', chrome.runtime.lastError.message);
          } else if (response && response.success) {
            this.addLogEntry({
              type: 'SYSTEM',
              message: 'Video recording stopped and saved',
              timestamp: new Date()
            });
            this.currentSession.videoRecording = false;

            // Notify popup about video recording status
            this.notifyVideoRecordingStatus(false);
          }
        });
      }
    } catch (error) {
      console.error('BugReplay: Error stopping video recording:', error);
      this.addLogEntry({
        type: 'ERROR',
        message: `Error stopping video recording: ${error.message}`,
        timestamp: new Date()
      });
    }
  }

  /**
   * Handle video recording data from content script
   */
  handleVideoRecordingData(videoData) {
    try {
      if (this.currentSession && videoData) {
        this.currentSession.videoUrl = videoData.videoUrl;
        this.currentSession.videoSize = videoData.videoSize;

        this.addLogEntry({
          type: 'SYSTEM',
          message: `Video recording processed (${this.formatFileSize(videoData.videoSize)})`,
          timestamp: new Date()
        });

        console.log('BugReplay: Video recording data received, size:', videoData.videoSize);
      }
    } catch (error) {
      console.error('BugReplay: Error handling video recording data:', error);
      this.addLogEntry({
        type: 'ERROR',
        message: `Error processing video recording: ${error.message}`,
        timestamp: new Date()
      });
    }
  }

  /**
   * Notify popup about video recording status changes
   * @param {boolean} isRecording - Whether video recording is active
   */
  notifyVideoRecordingStatus(isRecording) {
    try {
      // Send message to popup if it's open
      chrome.runtime.sendMessage({
        type: 'VIDEO_RECORDING_STATUS',
        isRecording: isRecording
      }).catch(() => {
        // Popup might not be open, which is fine
      });
    } catch (error) {
      // Ignore errors - popup might not be open
    }
  }

  /**
   * Load settings from Chrome storage
   */
  async loadSettings() {
    try {
      if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.sync) {
        chrome.storage.sync.get(['bugReplaySettings'], (result) => {
          if (result.bugReplaySettings) {
            this.settings = { ...this.settings, ...result.bugReplaySettings };
            console.log('BugReplay: Settings loaded:', this.settings);
          }
        });
      }
    } catch (error) {
      console.error('BugReplay: Error loading settings:', error);
    }
  }

  /**
   * Update settings
   * @param {Object} newSettings - New settings to apply
   */
  updateSettings(newSettings) {
    this.settings = { ...this.settings, ...newSettings };
    console.log('BugReplay: Settings updated:', this.settings);
  }

  /**
   * Format file size for display
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Add a log entry to the current session
   * @param {Object} logEntry - Log entry to add
   */
  addLogEntry(logEntry) {
    if (!this.currentSession) return;

    const entry = {
      id: Date.now() + Math.random(),
      timestamp: logEntry.timestamp ? (typeof logEntry.timestamp === 'string' ? new Date(logEntry.timestamp) : logEntry.timestamp) : new Date(),
      ...logEntry
    };

    this.currentSession.logs.push(entry);

    // Save session periodically to persist logs
    this.saveSession().catch(error => {
      console.warn('BugReplay: Failed to save session after adding log:', error);
    });
  }

  /**
   * Generate comprehensive HAR (HTTP Archive) data from network entries
   */
  generateHarData() {
    const startTime = this.currentSession.startTime;
    const pageUrl = this.currentSession.url;
    const pageTitle = this.currentSession.title;

    // Sort entries by start time for better organization
    const sortedEntries = this.networkEntries.sort((a, b) => a.startTime - b.startTime);

    this.currentSession.harData = {
      log: {
        version: '1.2',
        creator: {
          name: 'BugReplay Chrome Extension',
          version: '1.0.1',
          comment: 'Generated by BugReplay with enhanced network debugging capabilities'
        },
        browser: {
          name: 'Chrome',
          version: this.getBrowserVersion(),
          comment: 'Chrome browser with BugReplay extension'
        },
        pages: [{
          startedDateTime: new Date(startTime).toISOString(),
          id: `page_${this.currentSession.id}`,
          title: pageTitle || 'Unknown Page',
          pageTimings: {
            onContentLoad: -1,
            onLoad: -1,
            comment: 'Page timing data not available from extension context'
          },
          comment: `Recorded session: ${this.currentSession.id}`
        }],
        entries: sortedEntries.map(entry => this.generateHarEntry(entry, pageUrl)),
        comment: `HAR file generated from BugReplay session with ${sortedEntries.length} network requests`
      }
    };

    console.log(`BugReplay: Generated enhanced HAR data with ${this.networkEntries.length} network entries`);
  }

  /**
   * Get browser version information
   * @returns {string} Browser version
   */
  getBrowserVersion() {
    const userAgent = navigator.userAgent;
    const chromeMatch = userAgent.match(/Chrome\/([0-9.]+)/);
    const edgeMatch = userAgent.match(/Edg\/([0-9.]+)/);

    if (edgeMatch) {
      return `Edge ${edgeMatch[1]}`;
    } else if (chromeMatch) {
      return `Chrome ${chromeMatch[1]}`;
    }

    return 'Unknown';
  }

  /**
   * Generate a single HAR entry from network data
   * @param {Object} entry - Network entry data
   * @param {string} pageUrl - Page URL for reference
   * @returns {Object} HAR entry
   */
  generateHarEntry(entry, pageUrl) {
    // Parse URL for query string with proper encoding
    let queryString = [];
    try {
      const url = new URL(entry.url);
      queryString = Array.from(url.searchParams.entries()).map(([name, value]) => ({
        name: decodeURIComponent(name),
        value: decodeURIComponent(value),
        comment: ''
      }));
    } catch (error) {
      // Handle malformed URLs
      console.warn('BugReplay: Could not parse URL for query string:', entry.url);
    }

    // Process request headers with better formatting
    const requestHeaders = (entry.requestHeaders || []).map(header => ({
      name: header.name || '',
      value: header.value || '',
      comment: ''
    }));

    // Process response headers with better formatting
    const responseHeaders = (entry.responseHeaders || []).map(header => ({
      name: header.name || '',
      value: header.value || '',
      comment: ''
    }));

    // Extract cookies from headers
    const cookies = this.extractCookies(requestHeaders, responseHeaders);

    // Calculate header sizes
    const requestHeadersSize = this.calculateHeadersSize(entry.requestHeaders);
    const responseHeadersSize = this.calculateHeadersSize(entry.responseHeaders);

    // Process request body
    let postData = undefined;
    if (entry.requestBody && entry.method !== 'GET') {
      postData = this.processRequestBody(entry.requestBody, entry.requestHeaders);
    }

    // Calculate content size and compression
    const contentLength = this.getHeaderValue(entry.responseHeaders, 'content-length');
    const contentEncoding = this.getHeaderValue(entry.responseHeaders, 'content-encoding');
    const transferSize = entry.transferSize || entry.size || 0;
    const contentSize = contentLength ? parseInt(contentLength) : transferSize;

    // Generate timing data
    const timings = this.generateTimingData(entry);

    return {
      pageref: `page_${this.currentSession.id}`,
      startedDateTime: new Date(entry.startTime).toISOString(),
      time: entry.endTime ? entry.endTime - entry.startTime : -1,
      request: {
        method: entry.method,
        url: entry.url,
        httpVersion: 'HTTP/1.1',
        headers: requestHeaders,
        queryString: queryString,
        postData: postData,
        headersSize: requestHeadersSize,
        bodySize: postData ? (postData.text?.length || 0) : 0,
        comment: ''
      },
      response: {
        status: entry.status || 0,
        statusText: entry.statusText || (entry.error ? entry.error : ''),
        httpVersion: 'HTTP/1.1',
        headers: responseHeaders,
        content: {
          size: contentSize,
          compression: contentEncoding ? contentSize - transferSize : 0,
          mimeType: entry.mimeType || this.getHeaderValue(entry.responseHeaders, 'content-type') || 'application/octet-stream',
          text: '',
          encoding: contentEncoding ? 'base64' : undefined,
          comment: 'Response body not captured by extension'
        },
        redirectURL: this.getHeaderValue(entry.responseHeaders, 'location') || '',
        headersSize: responseHeadersSize,
        bodySize: transferSize,
        transferSize: transferSize,
        comment: ''
      },
      cache: {
        beforeRequest: null,
        afterRequest: null,
        comment: 'Cache information not available from extension context'
      },
      timings: timings,
      serverIPAddress: '',
      connection: '',
      comment: entry.error ? `Error: ${entry.error}` : ''
    };
  }

  /**
   * Calculate the size of headers in bytes
   * @param {Array} headers - Array of header objects
   * @returns {number} Size in bytes
   */
  calculateHeadersSize(headers) {
    if (!headers || !Array.isArray(headers)) return -1;

    return headers.reduce((size, header) => {
      return size + header.name.length + header.value.length + 4; // +4 for ": " and "\r\n"
    }, 0);
  }

  /**
   * Get header value by name (case-insensitive)
   * @param {Array} headers - Array of header objects
   * @param {string} name - Header name to find
   * @returns {string|null} Header value or null
   */
  getHeaderValue(headers, name) {
    if (!headers || !Array.isArray(headers)) return null;

    const header = headers.find(h => h.name.toLowerCase() === name.toLowerCase());
    return header ? header.value : null;
  }

  /**
   * Process request body data for HAR format
   * @param {Object} requestBody - Request body data
   * @param {Array} headers - Request headers
   * @returns {Object|undefined} Processed post data
   */
  processRequestBody(requestBody, headers) {
    if (!requestBody) return undefined;

    const contentType = this.getHeaderValue(headers, 'content-type') || 'application/octet-stream';

    try {
      if (requestBody.formData) {
        // Handle form data
        const params = [];
        for (const [key, values] of Object.entries(requestBody.formData)) {
          for (const value of values) {
            params.push({ name: key, value: value });
          }
        }
        return {
          mimeType: 'application/x-www-form-urlencoded',
          params: params,
          text: '',
          comment: 'Form data'
        };
      } else if (requestBody.raw) {
        // Handle raw data
        const rawData = requestBody.raw[0];
        if (rawData && rawData.bytes) {
          const decoder = new TextDecoder();
          const text = decoder.decode(new Uint8Array(rawData.bytes));
          return {
            mimeType: contentType,
            text: text,
            comment: 'Raw request body'
          };
        }
      }
    } catch (error) {
      console.warn('BugReplay: Error processing request body:', error);
    }

    return {
      mimeType: contentType,
      text: '',
      comment: 'Request body could not be processed'
    };
  }

  /**
   * Generate timing data for HAR entry
   * @param {Object} entry - Network entry
   * @returns {Object} Timing data
   */
  generateTimingData(entry) {
    const timings = {
      blocked: entry.timings.blocked >= 0 ? entry.timings.blocked : -1,
      dns: entry.timings.dns >= 0 ? entry.timings.dns : -1,
      connect: entry.timings.connect >= 0 ? entry.timings.connect : -1,
      send: entry.timings.send >= 0 ? entry.timings.send : 0,
      wait: entry.timings.wait >= 0 ? entry.timings.wait : (entry.endTime ? entry.endTime - entry.startTime : -1),
      receive: entry.timings.receive >= 0 ? entry.timings.receive : 0,
      ssl: entry.timings.ssl >= 0 ? entry.timings.ssl : -1,
      comment: 'Timing data collected from webRequest API'
    };

    return timings;
  }

  /**
   * Extract cookies from request and response headers
   * @param {Array} requestHeaders - Request headers
   * @param {Array} responseHeaders - Response headers
   * @returns {Array} Cookie objects
   */
  extractCookies(requestHeaders, responseHeaders) {
    const cookies = [];

    // Extract cookies from request headers
    const cookieHeader = requestHeaders.find(h => h.name.toLowerCase() === 'cookie');
    if (cookieHeader) {
      const cookiePairs = cookieHeader.value.split(';');
      cookiePairs.forEach(pair => {
        const [name, value] = pair.trim().split('=');
        if (name && value) {
          cookies.push({
            name: name.trim(),
            value: value.trim(),
            comment: 'Request cookie'
          });
        }
      });
    }

    return cookies;
  }

  /**
   * Process request body for HAR format
   * @param {Object} requestBody - Request body from webRequest
   * @param {Array} requestHeaders - Request headers
   * @returns {Object} Processed post data
   */
  processRequestBody(requestBody, requestHeaders) {
    if (!requestBody) return undefined;

    // Get content type from headers
    const contentTypeHeader = requestHeaders?.find(h => h.name.toLowerCase() === 'content-type');
    const contentType = contentTypeHeader?.value || 'application/octet-stream';

    if (requestBody.formData) {
      // Handle form data
      const params = [];
      Object.entries(requestBody.formData).forEach(([key, values]) => {
        values.forEach(value => {
          params.push({
            name: key,
            value: value,
            comment: ''
          });
        });
      });

      return {
        mimeType: 'application/x-www-form-urlencoded',
        params: params,
        text: params.map(p => `${encodeURIComponent(p.name)}=${encodeURIComponent(p.value)}`).join('&'),
        comment: 'Form data'
      };
    } else if (requestBody.raw) {
      // Handle raw data
      const rawData = requestBody.raw[0];
      if (rawData && rawData.bytes) {
        try {
          const decoder = new TextDecoder('utf-8');
          const text = decoder.decode(new Uint8Array(rawData.bytes));

          return {
            mimeType: contentType.split(';')[0],
            params: [],
            text: text,
            comment: `Raw data (${rawData.bytes.length} bytes)`
          };
        } catch (error) {
          return {
            mimeType: contentType.split(';')[0],
            params: [],
            text: '[Binary data]',
            comment: `Binary data (${rawData.bytes.length} bytes)`
          };
        }
      }
    }

    return undefined;
  }

  /**
   * Process and finalize recording data
   */
  async processRecordingData() {
    // Additional processing can be added here
    console.log('Processing recording data for session:', this.currentSession.id);
  }

  /**
   * Save session to chrome storage with quota management
   */
  async saveSession() {
    if (!this.currentSession) return;

    try {
      // Check storage usage before saving
      const usage = await this.getStorageUsage();
      const sessionSize = this.calculateSessionSize();

      // If we're approaching quota limits, clean up old data
      if (usage.percentage > 80 || (usage.available < sessionSize * 2)) {
        console.log('BugReplay: Storage quota approaching, cleaning up old data...');
        await this.cleanupOldSessions();
      }

      const sessionData = { ...this.currentSession };
      await chrome.storage.local.set({
        [`session_${sessionData.id}`]: sessionData,
        'currentSessionId': sessionData.id,
        'recordingState': this.state
      });

      console.log(`BugReplay: Session saved. Storage usage: ${usage.percentage}%`);
    } catch (error) {
      if (error.message && error.message.includes('quota')) {
        console.error('BugReplay: Storage quota exceeded, attempting cleanup...');
        await this.handleStorageQuotaExceeded();

        // Try saving again after cleanup
        try {
          const sessionData = { ...this.currentSession };
          await chrome.storage.local.set({
            [`session_${sessionData.id}`]: sessionData,
            'currentSessionId': sessionData.id,
            'recordingState': this.state
          });
        } catch (retryError) {
          console.error('BugReplay: Failed to save session even after cleanup:', retryError);
          this.addLogEntry({
            type: 'ERROR',
            message: 'Storage quota exceeded. Some data may be lost. Please export or delete old sessions.'
          });
        }
      } else {
        console.error('BugReplay: Error saving session:', error);
        throw error;
      }
    }
  }

  /**
   * Restore session from chrome storage
   * @param {string} sessionId - Session ID to restore
   */
  async restoreSession(sessionId) {
    try {
      const result = await chrome.storage.local.get([`session_${sessionId}`]);
      const sessionData = result[`session_${sessionId}`];

      if (!sessionData) {
        console.warn('BugReplay: No session data found for ID:', sessionId);
        return false;
      }

      // Restore session data
      this.currentSession = {
        ...sessionData,
        startTime: new Date(sessionData.startTime),
        endTime: sessionData.endTime ? new Date(sessionData.endTime) : null
      };

      this.state = sessionData.state || 'RECORDING';

      // Restore recording components if session was recording
      if (this.state === 'RECORDING') {
        await this.initializeNetworkMonitoring();
        await this.startScreenshotCapture();
        console.log('BugReplay: Recording session restored successfully:', sessionId);
      }

      return true;
    } catch (error) {
      console.error('BugReplay: Error restoring session:', error);
      return false;
    }
  }

  /**
   * Save completed session permanently
   */
  async saveCompletedSession() {
    if (!this.currentSession) return;

    const sessionData = {
      ...this.currentSession,
      completedAt: new Date(),
      size: this.calculateSessionSize(),
      status: 'completed'
    };

    try {
      // Save to completed sessions list
      const result = await chrome.storage.local.get(['completedSessions']);
      const completedSessions = result.completedSessions || [];

      // Add new session to the list
      completedSessions.push({
        id: sessionData.id,
        title: sessionData.title,
        url: sessionData.url,
        startTime: sessionData.startTime,
        endTime: sessionData.endTime,
        completedAt: sessionData.completedAt,
        duration: sessionData.endTime ? sessionData.endTime - sessionData.startTime : 0,
        size: sessionData.size,
        status: sessionData.status,
        screenshotCount: sessionData.screenshots?.length || 0,
        logCount: sessionData.logs?.length || 0,
        networkRequestCount: sessionData.harData?.log?.entries?.length || 0
      });

      // Save both the session data and the updated list
      await chrome.storage.local.set({
        [`completed_session_${sessionData.id}`]: sessionData,
        'completedSessions': completedSessions
      });

      console.log('BugReplay: Session saved permanently:', sessionData.id);
    } catch (error) {
      console.error('BugReplay: Error saving completed session:', error);
    }
  }

  /**
   * Clear only active session data, keep completed sessions
   */
  async clearActiveSession() {
    try {
      await chrome.storage.local.remove([
        'currentSessionId',
        'recordingState'
      ]);
    } catch (error) {
      console.error('BugReplay: Error clearing active session:', error);
    }
  }

  /**
   * Calculate session data size (approximate)
   * @returns {number} Size in bytes
   */
  calculateSessionSize() {
    if (!this.currentSession) return 0;

    let size = 0;

    // Calculate approximate size
    size += JSON.stringify(this.currentSession.logs || []).length;
    size += (this.currentSession.screenshots || []).reduce((acc, screenshot) => {
      if (typeof screenshot === 'string') {
        return acc + screenshot.length; // String format (automatic screenshots)
      } else if (screenshot && screenshot.dataUrl) {
        return acc + screenshot.dataUrl.length; // Object format (manual screenshots)
      }
      return acc;
    }, 0);
    size += JSON.stringify(this.currentSession.harData || {}).length;

    return size;
  }

  /**
   * Get all completed sessions
   * @returns {Promise<Array>} List of completed sessions
   */
  async getCompletedSessions() {
    try {
      const result = await chrome.storage.local.get(['completedSessions']);
      return result.completedSessions || [];
    } catch (error) {
      console.error('BugReplay: Error getting completed sessions:', error);
      return [];
    }
  }

  /**
   * Delete a specific session
   * @param {string} sessionId - ID of session to delete
   */
  async deleteSession(sessionId) {
    try {
      // Remove session data
      await chrome.storage.local.remove([`completed_session_${sessionId}`]);

      // Update completed sessions list
      const sessions = await this.getCompletedSessions();
      const updatedSessions = sessions.filter(session => session.id !== sessionId);
      await chrome.storage.local.set({ 'completedSessions': updatedSessions });

      console.log(`BugReplay: Session ${sessionId} deleted successfully`);
    } catch (error) {
      console.error(`BugReplay: Error deleting session ${sessionId}:`, error);
    }
  }

  /**
   * Get a specific completed session by ID
   * @param {string} sessionId - Session ID
   * @returns {Promise<Object|null>} Session data or null
   */
  async getCompletedSession(sessionId) {
    try {
      const result = await chrome.storage.local.get([`completed_session_${sessionId}`]);
      return result[`completed_session_${sessionId}`] || null;
    } catch (error) {
      console.error('BugReplay: Error getting completed session:', error);
      return null;
    }
  }

  /**
   * Delete a completed session
   * @param {string} sessionId - Session ID to delete
   * @returns {Promise<boolean>} Success status
   */
  async deleteCompletedSession(sessionId) {
    try {
      // Remove session data
      await chrome.storage.local.remove([`completed_session_${sessionId}`]);

      // Update completed sessions list
      const result = await chrome.storage.local.get(['completedSessions']);
      const completedSessions = result.completedSessions || [];
      const updatedSessions = completedSessions.filter(session => session.id !== sessionId);

      await chrome.storage.local.set({ 'completedSessions': updatedSessions });

      console.log('BugReplay: Session deleted:', sessionId);
      return true;
    } catch (error) {
      console.error('BugReplay: Error deleting session:', error);
      return false;
    }
  }

  /**
   * Delete multiple completed sessions
   * @param {string[]} sessionIds - Array of session IDs to delete
   * @returns {Promise<number>} Number of sessions deleted
   */
  async deleteMultipleSessions(sessionIds) {
    let deletedCount = 0;

    for (const sessionId of sessionIds) {
      const success = await this.deleteCompletedSession(sessionId);
      if (success) deletedCount++;
    }

    return deletedCount;
  }

  /**
   * Get storage usage statistics
   * @returns {Promise<Object>} Storage usage info
   */
  async getStorageUsage() {
    try {
      const usage = await chrome.storage.local.getBytesInUse();
      const quota = chrome.storage.local.QUOTA_BYTES || 10485760; // 10MB default (increased from 5MB)

      const sessions = await this.getCompletedSessions();

      // Calculate actual session data sizes for accurate average
      let totalSessionSize = 0;
      if (sessions.length > 0) {
        for (const session of sessions) {
          // Use the stored size if available, otherwise calculate it
          if (session.size) {
            totalSessionSize += session.size;
          } else {
            // Fallback: get the actual session data and calculate size
            const sessionData = await this.getCompletedSession(session.id);
            if (sessionData) {
              const calculatedSize = this.calculateSessionSize.call({ currentSession: sessionData });
              totalSessionSize += calculatedSize;
            }
          }
        }
      }

      return {
        used: usage,
        quota: quota,
        available: quota - usage,
        percentage: Math.round((usage / quota) * 100),
        sessionCount: sessions.length,
        averageSessionSize: sessions.length > 0 ? Math.round(totalSessionSize / sessions.length) : 0,
        totalSessionSize: totalSessionSize
      };
    } catch (error) {
      console.error('BugReplay: Error getting storage usage:', error);
      return {
        used: 0,
        quota: 10485760,
        available: 10485760,
        percentage: 0,
        sessionCount: 0,
        averageSessionSize: 0,
        totalSessionSize: 0
      };
    }
  }

  /**
   * Clean up old sessions to free storage space
   */
  async cleanupOldSessions() {
    try {
      const sessions = await this.getCompletedSessions();

      if (sessions.length <= 5) {
        console.log('BugReplay: Not enough sessions to clean up');
        return;
      }

      // Sort sessions by date (oldest first)
      sessions.sort((a, b) => new Date(a.startTime) - new Date(b.startTime));

      // Remove oldest sessions, keeping at least 5 most recent
      const sessionsToRemove = sessions.slice(0, sessions.length - 5);

      console.log(`BugReplay: Cleaning up ${sessionsToRemove.length} old sessions`);

      for (const session of sessionsToRemove) {
        await this.deleteSession(session.id);
      }

      console.log('BugReplay: Old sessions cleaned up successfully');
    } catch (error) {
      console.error('BugReplay: Error cleaning up old sessions:', error);
    }
  }

  /**
   * Handle storage quota exceeded error
   */
  async handleStorageQuotaExceeded() {
    try {
      console.log('BugReplay: Handling storage quota exceeded...');

      // First, try to clean up old sessions
      await this.cleanupOldSessions();

      // If still not enough space, remove screenshots from current session
      if (this.currentSession && this.currentSession.screenshots) {
        console.log('BugReplay: Removing screenshots to free space...');
        this.currentSession.screenshots = this.currentSession.screenshots.slice(-5); // Keep only last 5 screenshots
      }

      // Clear any temporary data
      await chrome.storage.local.remove(['tempData', 'cachedData']);

      console.log('BugReplay: Storage cleanup completed');
    } catch (error) {
      console.error('BugReplay: Error handling storage quota exceeded:', error);
    }
  }

  /**
   * Import a session from external data
   * @param {Object} sessionData - Session data to import
   * @returns {Promise<Object>} Import result
   */
  async importSession(sessionData) {
    try {
      // Generate new ID if there's a conflict
      let importId = sessionData.id;
      const existingSessions = await this.getCompletedSessions();
      const existingIds = existingSessions.map(s => s.id);

      if (existingIds.includes(importId)) {
        // Generate new ID with timestamp suffix
        const timestamp = Date.now();
        importId = `${sessionData.id}_imported_${timestamp}`;
        sessionData.id = importId;
      }

      // Ensure required fields and format
      const importedSession = {
        ...sessionData,
        id: importId,
        startTime: new Date(sessionData.startTime),
        endTime: sessionData.endTime ? new Date(sessionData.endTime) : null,
        completedAt: new Date(),
        size: this.calculateImportedSessionSize(sessionData),
        status: 'imported',
        imported: true,
        importedAt: new Date()
      };

      // Save the complete session data
      await chrome.storage.local.set({
        [`completed_session_${importId}`]: importedSession
      });

      // Add to completed sessions list
      const result = await chrome.storage.local.get(['completedSessions']);
      const completedSessions = result.completedSessions || [];

      completedSessions.push({
        id: importedSession.id,
        title: importedSession.title || 'Imported Session',
        url: importedSession.url,
        startTime: importedSession.startTime,
        endTime: importedSession.endTime,
        completedAt: importedSession.completedAt,
        duration: importedSession.endTime ?
          new Date(importedSession.endTime) - new Date(importedSession.startTime) : 0,
        size: importedSession.size,
        status: importedSession.status,
        screenshotCount: importedSession.screenshots?.length || 0,
        logCount: importedSession.logs?.length || 0,
        networkRequestCount: importedSession.harData?.log?.entries?.length || 0,
        imported: true
      });

      await chrome.storage.local.set({ completedSessions });

      console.log('BugReplay: Session imported successfully:', importId);
      return {
        success: true,
        sessionId: importId,
        message: 'Session imported successfully'
      };

    } catch (error) {
      console.error('BugReplay: Error importing session:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Calculate size of imported session data
   * @param {Object} sessionData - Session data
   * @returns {number} Size in bytes
   */
  calculateImportedSessionSize(sessionData) {
    let size = 0;

    try {
      // Calculate approximate size
      size += JSON.stringify(sessionData.logs || []).length;
      size += (sessionData.screenshots || []).reduce((acc, screenshot) => {
        if (typeof screenshot === 'string') {
          return acc + screenshot.length; // String format (automatic screenshots)
        } else if (screenshot && screenshot.dataUrl) {
          return acc + screenshot.dataUrl.length; // Object format (manual screenshots)
        }
        return acc;
      }, 0);
      size += JSON.stringify(sessionData.harData || {}).length;
      size += JSON.stringify({
        id: sessionData.id,
        title: sessionData.title,
        url: sessionData.url,
        startTime: sessionData.startTime,
        endTime: sessionData.endTime
      }).length;
    } catch (error) {
      console.warn('BugReplay: Error calculating imported session size:', error);
    }

    return size;
  }

  /**
   * Get tab information
   * @param {number} tabId - Tab ID
   * @returns {Promise<Object>} Tab information
   */
  async getTabInfo(tabId) {
    return new Promise((resolve, reject) => {
      chrome.tabs.get(tabId, (tab) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
          return;
        }
        resolve(tab);
      });
    });
  }

  /**
   * Generate unique session ID
   * @returns {string} Session ID
   */
  generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get current recording state with validation
   * @returns {Object} State information
   */
  getState() {
    // Validate state consistency
    if (this.state === 'RECORDING' && !this.currentSession) {
      console.warn('BugReplay: Inconsistent state detected - recording without session');
      this.forceResetState();
      return {
        state: 'IDLE',
        isRecording: false,
        sessionId: null,
        error: 'State was reset due to inconsistency'
      };
    }

    return {
      state: this.state,
      isRecording: this.state === 'RECORDING',
      sessionId: this.currentSession?.id || null,
      tabId: this.currentSession?.tabId || null
    };
  }

  /**
   * Get current session
   * @returns {Object|null} Current session or null
   */
  getCurrentSession() {
    return this.currentSession;
  }
}

// Initialize recording manager
const recordingManager = new RecordingManager();

// Restore recording state on startup
async function initializeExtension() {
  try {
    const result = await chrome.storage.local.get(['currentSessionId', 'recordingState']);
    if (result.currentSessionId && result.recordingState === 'RECORDING') {
      console.log('BugReplay: Restoring recording session:', result.currentSessionId);
      await recordingManager.restoreSession(result.currentSessionId);
    }
  } catch (error) {
    console.error('BugReplay: Error restoring session:', error);
  }
}

if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onInstalled) {
  chrome.runtime.onInstalled.addListener(() => {
    console.log('BugReplay extension installed/updated.');
  });
}

if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onStartup) {
  chrome.runtime.onStartup.addListener(() => {
    console.log('BugReplay extension startup.');
    initializeExtension();
  });
}

// Initialize on script load
initializeExtension();

if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onMessage) {
  chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    // Handle recording management requests
    if (request.type === 'START_RECORDING_BACKGROUND') {
      handleStartRecording(request, sender, sendResponse);
      return true;
    } else if (request.type === 'STOP_RECORDING_BACKGROUND') {
      handleStopRecording(request, sender, sendResponse);
      return true;
    } else if (request.type === 'GET_RECORDING_STATE') {
      const stateInfo = recordingManager.getState();
      sendResponse({
        ...stateInfo,
        session: recordingManager.getCurrentSession()
      });
      return false;
    } else if (request.type === 'GET_SESSION_LOGS') {
      const session = recordingManager.getCurrentSession();
      sendResponse({
        logs: session ? session.logs : [],
        sessionId: session ? session.id : null
      });
      return false;
    } else if (request.type === 'ADD_LOG_ENTRY') {
      recordingManager.addLogEntry(request.logEntry);
      sendResponse({ success: true });
      return false;
    } else if (request.type === 'VIDEO_RECORDING_DATA') {
      recordingManager.handleVideoRecordingData(request.videoData);
      sendResponse({ success: true });
      return false;
    } else if (request.type === 'REQUEST_DESKTOP_CAPTURE') {
      handleDesktopCaptureRequest(request, sender, sendResponse);
      return true;
    } else if (request.type === 'GET_COMPLETED_SESSIONS') {
      handleGetCompletedSessions(request, sender, sendResponse);
      return true;
    } else if (request.type === 'GET_SESSION_DETAILS') {
      handleGetSessionDetails(request, sender, sendResponse);
      return true;
    } else if (request.type === 'DELETE_SESSION') {
      handleDeleteSession(request, sender, sendResponse);
      return true;
    } else if (request.type === 'DELETE_MULTIPLE_SESSIONS') {
      handleDeleteMultipleSessions(request, sender, sendResponse);
      return true;
    } else if (request.type === 'GET_STORAGE_USAGE') {
      handleGetStorageUsage(request, sender, sendResponse);
      return true;
    } else if (request.type === 'IMPORT_SESSION') {
      handleImportSession(request, sender, sendResponse);
      return true;
    } else if (request.type === 'CAPTURE_MANUAL_SCREENSHOT') {
      handleCaptureManualScreenshot(request, sender, sendResponse);
      return true;
    } else if (request.type === 'START_VIDEO_RECORDING_MANUAL') {
      handleStartVideoRecordingManual(request, sender, sendResponse);
      return true;
    } else if (request.type === 'SETTINGS_UPDATED') {
      handleSettingsUpdate(request, sender, sendResponse);
      return true;
    } else if (request.type === 'FORCE_RESET_RECORDING') {
      handleForceResetRecording(request, sender, sendResponse);
      return true;
    } else if (request.type === 'GET_ACTIVE_TAB_INFO') {
      if (sender.tab && (sender.tab.url || sender.tab.title)) { // Message from content script, which has tab info
        sendResponse({ title: sender.tab.title, url: sender.tab.url });
      } else { // Message likely from popup, query for active tab
        if (chrome.tabs && chrome.tabs.query) {
          chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            if (chrome.runtime.lastError) {
              console.error('Background: Error querying active tab for GET_ACTIVE_TAB_INFO:', chrome.runtime.lastError.message);
              sendResponse({ error: `Background: Error querying active tab: ${chrome.runtime.lastError.message}` });
              return;
            }
            if (tabs && tabs[0]) {
              sendResponse({ title: tabs[0].title || tabs[0].url, url: tabs[0].url });
            } else {
              sendResponse({ error: "Background: No active tab found for GET_ACTIVE_TAB_INFO." });
            }
          });
        } else {
          sendResponse({ error: "Background: chrome.tabs.query not available for GET_ACTIVE_TAB_INFO." });
        }
      }
      return true; // Indicates that the response is sent asynchronously
    } else if (request.target === 'content_script_via_background') {
      if (chrome.tabs && chrome.tabs.query && chrome.tabs.sendMessage) {
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
          if (chrome.runtime.lastError) {
            console.error('Background: Error querying active tab for relay:', chrome.runtime.lastError.message);
            sendResponse({ error: `Background: Error querying active tab for relay: ${chrome.runtime.lastError.message}` });
            return;
          }
          if (tabs && tabs[0] && tabs[0].id) {
            const messageForContentScript = { ...request };
            delete messageForContentScript.target; // Remove the routing target property
            
            chrome.tabs.sendMessage(tabs[0].id, messageForContentScript, (responseFromContent) => {
              if (chrome.runtime.lastError) {
                // Content script might not be there or might not respond.
                // This isn't always a critical error for the background script itself,
                // but the popup needs to know.
                console.warn('Background: Error sending to content script or no response:', chrome.runtime.lastError.message);
                sendResponse({ error: `Background: Error sending to content script: ${chrome.runtime.lastError.message}`, details: responseFromContent });
                return;
              }
              sendResponse(responseFromContent);
            });
          } else {
            sendResponse({ error: "Background: No active tab found to relay message." });
          }
        });
      } else {
        sendResponse({ error: "Background: chrome.tabs API not available for relay." });
      }
      return true; // Asynchronous response
    }
    // Add more message handlers if needed, ensure 'return true' for async.
  });
}

/**
 * Ensure content script is injected into the tab
 * @param {number} tabId - Tab ID
 * @returns {Promise<boolean>} Success status
 */
async function ensureContentScriptInjected(tabId) {
  try {
    // Try to ping the content script first
    const pingResponse = await new Promise((resolve) => {
      chrome.tabs.sendMessage(tabId, { type: 'PING' }, (response) => {
        if (chrome.runtime.lastError) {
          resolve(null);
        } else {
          resolve(response);
        }
      });
    });

    if (pingResponse && pingResponse.pong) {
      console.log('BugReplay: Content script already active');
      return true;
    }

    // Content script not responding, inject it
    console.log('BugReplay: Injecting content script into tab', tabId);
    await chrome.scripting.executeScript({
      target: { tabId: tabId },
      files: ['content.js']
    });

    // Wait a moment for the script to initialize
    await new Promise(resolve => setTimeout(resolve, 100));

    return true;
  } catch (error) {
    console.error('BugReplay: Failed to inject content script:', error);
    return false;
  }
}

/**
 * Handle start recording request
 * @param {Object} request - Message request
 * @param {Object} sender - Message sender
 * @param {Function} sendResponse - Response callback
 */
async function handleStartRecording(request, sender, sendResponse) {
  try {
    // Check current state first
    const currentState = recordingManager.getState();

    if (currentState.isRecording) {
      console.log('BugReplay: Recording already in progress, returning existing session');
      sendResponse({
        success: true,
        sessionId: currentState.sessionId,
        message: 'Recording already in progress',
        alreadyRecording: true
      });
      return;
    }

    // Get active tab
    const tabs = await new Promise((resolve, reject) => {
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
          return;
        }
        resolve(tabs);
      });
    });

    if (!tabs || !tabs[0]) {
      throw new Error('No active tab found');
    }

    const tabId = tabs[0].id;

    // Ensure content script is injected
    const contentScriptReady = await ensureContentScriptInjected(tabId);
    if (!contentScriptReady) {
      throw new Error('Failed to inject content script');
    }

    const sessionId = await recordingManager.startRecording(tabId);

    // Notify content script to start recording
    chrome.tabs.sendMessage(tabId, {
      type: 'START_RECORDING_CONTENT',
      sessionId: sessionId
    }, (response) => {
      if (chrome.runtime.lastError) {
        console.warn('Content script communication error:', chrome.runtime.lastError.message);
      } else {
        console.log('Content script recording started:', response);
      }
    });

    sendResponse({
      success: true,
      sessionId: sessionId,
      message: 'Recording started successfully'
    });
  } catch (error) {
    console.error('Failed to start recording:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

/**
 * Handle stop recording request
 * @param {Object} request - Message request
 * @param {Object} sender - Message sender
 * @param {Function} sendResponse - Response callback
 */
async function handleStopRecording(request, sender, sendResponse) {
  try {
    const session = await recordingManager.stopRecording();

    // Notify content script to stop recording
    if (session.tabId) {
      chrome.tabs.sendMessage(session.tabId, {
        type: 'STOP_RECORDING_CONTENT',
        sessionId: session.id
      }, (response) => {
        if (chrome.runtime.lastError) {
          console.warn('Content script communication error during stop:', chrome.runtime.lastError.message);
        } else {
          console.log('Content script recording stopped:', response);
        }
      });
    }

    sendResponse({
      success: true,
      session: session,
      message: 'Recording stopped successfully'
    });
  } catch (error) {
    console.error('Failed to stop recording:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

/**
 * Handle get completed sessions request
 */
async function handleGetCompletedSessions(request, sender, sendResponse) {
  try {
    const sessions = await recordingManager.getCompletedSessions();
    sendResponse({
      success: true,
      sessions: sessions
    });
  } catch (error) {
    console.error('Failed to get completed sessions:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

/**
 * Handle get session details request
 */
async function handleGetSessionDetails(request, sender, sendResponse) {
  try {
    const session = await recordingManager.getCompletedSession(request.sessionId);
    sendResponse({
      success: true,
      session: session
    });
  } catch (error) {
    console.error('Failed to get session details:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

/**
 * Handle delete session request
 */
async function handleDeleteSession(request, sender, sendResponse) {
  try {
    const success = await recordingManager.deleteCompletedSession(request.sessionId);
    sendResponse({
      success: success,
      message: success ? 'Session deleted successfully' : 'Failed to delete session'
    });
  } catch (error) {
    console.error('Failed to delete session:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

/**
 * Handle delete multiple sessions request
 */
async function handleDeleteMultipleSessions(request, sender, sendResponse) {
  try {
    const deletedCount = await recordingManager.deleteMultipleSessions(request.sessionIds);
    sendResponse({
      success: true,
      deletedCount: deletedCount,
      message: `${deletedCount} sessions deleted successfully`
    });
  } catch (error) {
    console.error('Failed to delete multiple sessions:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

/**
 * Handle get storage usage request
 */
async function handleGetStorageUsage(request, sender, sendResponse) {
  try {
    const usage = await recordingManager.getStorageUsage();
    sendResponse({
      success: true,
      usage: usage
    });
  } catch (error) {
    console.error('Failed to get storage usage:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

/**
 * Handle import session request
 */
async function handleImportSession(request, sender, sendResponse) {
  try {
    const result = await recordingManager.importSession(request.sessionData);
    sendResponse(result);
  } catch (error) {
    console.error('Failed to import session:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

/**
 * Handle manual screenshot capture request
 */
async function handleCaptureManualScreenshot(request, sender, sendResponse) {
  try {
    if (recordingManager.getState() !== 'RECORDING') {
      sendResponse({
        success: false,
        error: 'No active recording session'
      });
      return;
    }

    const screenshot = await recordingManager.captureManualScreenshot();
    sendResponse({
      success: true,
      screenshot: screenshot,
      message: 'Manual screenshot captured successfully'
    });
  } catch (error) {
    console.error('Failed to capture manual screenshot:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

/**
 * Handle manual video recording start request
 */
async function handleStartVideoRecordingManual(request, sender, sendResponse) {
  try {
    // Send message to the active tab to start video recording
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (tabs.length === 0) {
        sendResponse({
          success: false,
          error: 'No active tab found'
        });
        return;
      }

      const activeTab = tabs[0];
      chrome.tabs.sendMessage(activeTab.id, {
        type: 'START_VIDEO_RECORDING',
        sessionId: recordingManager.currentSession?.id
      }, (response) => {
        if (chrome.runtime.lastError) {
          sendResponse({
            success: false,
            error: chrome.runtime.lastError.message
          });
          return;
        }
        sendResponse(response);
      });
    });
  } catch (error) {
    console.error('Failed to start manual video recording:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

/**
 * Handle settings update request
 */
async function handleSettingsUpdate(request, sender, sendResponse) {
  try {
    if (request.settings) {
      recordingManager.updateSettings(request.settings);
      sendResponse({
        success: true,
        message: 'Settings updated successfully'
      });
    } else {
      sendResponse({
        success: false,
        error: 'No settings provided'
      });
    }
  } catch (error) {
    console.error('Failed to update settings:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

/**
 * Handle force reset recording request
 */
async function handleForceResetRecording(request, sender, sendResponse) {
  try {
    console.log('BugReplay: Force reset recording requested');
    await recordingManager.forceResetState();

    sendResponse({
      success: true,
      message: 'Recording state reset successfully'
    });
  } catch (error) {
    console.error('Failed to force reset recording:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

/**
 * Handle desktop capture request
 */
async function handleDesktopCaptureRequest(request, sender, sendResponse) {
  try {
    chrome.desktopCapture.chooseDesktopMedia(
      request.sources || ['tab'],
      sender.tab,
      (streamId) => {
        if (chrome.runtime.lastError) {
          sendResponse({
            success: false,
            error: chrome.runtime.lastError.message
          });
          return;
        }
        if (!streamId) {
          sendResponse({
            success: false,
            error: 'User cancelled screen capture'
          });
          return;
        }
        sendResponse({
          success: true,
          streamId: streamId
        });
      }
    );
  } catch (error) {
    console.error('Failed to request desktop capture:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}
